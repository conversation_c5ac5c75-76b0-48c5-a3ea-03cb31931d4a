import { useCallback, useEffect, useRef } from "react";
import { useModalControl } from "@snap/design-system";
import { KeyRound } from "lucide-react";
import { useUserData, useUserIsVerified, useUserActions } from "~/store/userStore";
import { useSecretKeyActions, getStoredEncryptionKey } from "~/store/secretKeyStore";
import { SecretKeyDialog } from "~/containers/report/SecretKeyDialog";
import { SecretKeyExpiredError } from "./useEncryption";

export const useSecretKeyValidation = () => {
  const { open } = useModalControl();
  const userData = useUserData();
  const isVerified = useUserIsVerified();
  const { setIsVerified } = useUserActions();
  const { clearSecretKey, restoreStoredKey, clearMemoryKey } = useSecretKeyActions();

  const openSecretKeyDialog = useCallback(() => {
    const handleOpenUserSecretKeyDialog = () => {
      open({
        modal: () => ({
          title: userData?.verifier ? "INSERIR SENHA" : "CRIAR SENHA",
          icon: <KeyRound />,
          content: <SecretKeyDialog.Content />,
          footer: <SecretKeyDialog.Footer onOpen={handleOpenUserSecretKeyDialog} />,
        }),
        config: {
          content: {
            className: "max-w-xl",
          },
        },
      });
    };

    handleOpenUserSecretKeyDialog();
  }, [open, userData?.verifier]);

  // Efeito para restaurar chave armazenada na inicialização ou abrir dialog
  useEffect(() => {
    if (userData && userData.salt && !isVerified) {
      const hasValidStoredKey = getStoredEncryptionKey();

      if (hasValidStoredKey) {
        try {
          restoreStoredKey();
          setIsVerified(true);
        } catch (error) {
          console.error("Erro ao restaurar chave armazenada:", error);
          clearSecretKey();
          // Se falhar ao restaurar, abre o diálogo
          openSecretKeyDialog();
        }
      } else {
        // Se não há chave armazenada, abre o diálogo imediatamente
        openSecretKeyDialog();
      }
    }
  }, [userData, isVerified, restoreStoredKey, setIsVerified, clearSecretKey, openSecretKeyDialog]);

  const validateSecretKey = useCallback(async (): Promise<boolean> => {
    try {
      // Verifica se há uma chave armazenada válida
      const hasValidStoredKey = getStoredEncryptionKey();

      if (hasValidStoredKey && userData?.salt) {
        try {
          // Tenta restaurar a chave armazenada
          restoreStoredKey();
          setIsVerified(true);
          return true;
        } catch (error) {
          console.error("Erro ao restaurar chave armazenada:", error);
          // Se falhar, limpa e continua para solicitar nova chave
          clearSecretKey();
        }
      }

      // Se não há chave válida ou já está verificado mas sem storage, abre o diálogo
      openSecretKeyDialog();
      return false;
    } catch (error) {
      console.error("Erro na validação da chave secreta:", error);
      openSecretKeyDialog();
      return false;
    }
  }, [userData?.salt, restoreStoredKey, setIsVerified, clearSecretKey, openSecretKeyDialog]);

  const handleSecretKeyError = useCallback((error: Error) => {
    if (error instanceof SecretKeyExpiredError) {
      // Limpa a chave expirada
      clearSecretKey();
      setIsVerified(false);

      // Abre o diálogo para nova autenticação
      openSecretKeyDialog();
      return true;
    }
    return false;
  }, [clearSecretKey, setIsVerified, openSecretKeyDialog]);

  const resetVerificationIfNoStorage = useCallback(() => {
    const hasStoredKey = getStoredEncryptionKey();
    if (!hasStoredKey) {
      // Para chaves sem storage, limpa apenas a chave da memória
      // NÃO reseta isVerified automaticamente - apenas quando necessário
      clearMemoryKey();
    }
  }, [clearMemoryKey]);

  const forceResetVerification = useCallback(() => {
    // Força reset da verificação (usado em navegação)
    setIsVerified(false);
    clearMemoryKey();
  }, [setIsVerified, clearMemoryKey]);

  return {
    validateSecretKey,
    handleSecretKeyError,
    openSecretKeyDialog,
    resetVerificationIfNoStorage,
    forceResetVerification,
    isVerified
  };
};
