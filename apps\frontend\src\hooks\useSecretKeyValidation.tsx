import { useCallback, useEffect } from "react";
import { useModalControl } from "@snap/design-system";
import { KeyRound } from "lucide-react";
import { useUserData, useUserIsVerified, useUserActions } from "~/store/userStore";
import { useSecretKeyActions, hasValidStoredKey, getEffectiveSecretKey } from "~/store/secretKeyStore";
import { SecretKeyDialog } from "~/containers/report/SecretKeyDialog";
import { SecretKeyExpiredError } from "./useEncryption";

export const useSecretKeyValidation = () => {
  const { open } = useModalControl();
  const userData = useUserData();
  const isVerified = useUserIsVerified();
  const { setIsVerified } = useUserActions();
  const { clearSecretKey, restoreStoredKey, clearMemoryKey, hasValidStoredKey: checkValidStoredKey } = useSecretKeyActions();

  const openSecretKeyDialog = useCallback(() => {
    const handleOpenUserSecretKeyDialog = () => {
      open({
        modal: () => ({
          title: userData?.verifier ? "INSERIR SENHA" : "CRIAR SENHA",
          icon: <KeyRound />,
          content: <SecretKeyDialog.Content />,
          footer: <SecretKeyDialog.Footer onOpen={handleOpenUserSecretKeyDialog} />,
        }),
        config: {
          content: {
            className: "max-w-xl",
          },
        },
      });
    };

    handleOpenUserSecretKeyDialog();
  }, [open, userData?.verifier]);

  // Efeito para verificar estado da chave e definir isVerified adequadamente
  useEffect(() => {
    if (userData && userData.salt) {
      const hasStoredKey = hasValidStoredKey();
      const effectiveKey = getEffectiveSecretKey();

      // Se não há chave válida (nem armazenada nem na memória), deve estar não verificado
      if (!hasStoredKey && !effectiveKey) {
        if (isVerified) {
          setIsVerified(false);
        }
        // Só abre o diálogo se não está verificado e não há chave
        if (!isVerified) {
          openSecretKeyDialog();
        }
      } else if (hasStoredKey && !isVerified) {
        // Se há chave armazenada válida mas não está verificado, tenta restaurar
        try {
          restoreStoredKey();
          setIsVerified(true);
        } catch (error) {
          console.error("Erro ao restaurar chave armazenada:", error);
          clearSecretKey();
          setIsVerified(false);
          openSecretKeyDialog();
        }
      }
    }
  }, [userData, isVerified, restoreStoredKey, setIsVerified, clearSecretKey, openSecretKeyDialog]);

  const validateSecretKey = useCallback(async (): Promise<boolean> => {
    try {
      // Verifica se há uma chave armazenada válida ou na memória
      const hasStoredKey = hasValidStoredKey();
      const effectiveKey = getEffectiveSecretKey();

      if ((hasStoredKey || effectiveKey) && userData?.salt) {
        if (hasStoredKey && !effectiveKey) {
          try {
            // Tenta restaurar a chave armazenada
            restoreStoredKey();
            setIsVerified(true);
            return true;
          } catch (error) {
            console.error("Erro ao restaurar chave armazenada:", error);
            // Se falhar, limpa e continua para solicitar nova chave
            clearSecretKey();
            setIsVerified(false);
          }
        } else if (effectiveKey) {
          // Já tem chave efetiva (memória ou restaurada)
          setIsVerified(true);
          return true;
        }
      }

      // Se não há chave válida, reseta verificação e abre o diálogo
      setIsVerified(false);
      openSecretKeyDialog();
      return false;
    } catch (error) {
      console.error("Erro na validação da chave secreta:", error);
      setIsVerified(false);
      openSecretKeyDialog();
      return false;
    }
  }, [userData?.salt, restoreStoredKey, setIsVerified, clearSecretKey, openSecretKeyDialog]);

  const handleSecretKeyError = useCallback((error: Error) => {
    if (error instanceof SecretKeyExpiredError) {
      // Limpa a chave expirada
      clearSecretKey();
      setIsVerified(false);

      // Abre o diálogo para nova autenticação
      openSecretKeyDialog();
      return true;
    }
    return false;
  }, [clearSecretKey, setIsVerified, openSecretKeyDialog]);

  const resetVerificationIfNoStorage = useCallback(() => {
    const hasStoredKey = hasValidStoredKey();
    const effectiveKey = getEffectiveSecretKey();

    if (!hasStoredKey && !effectiveKey) {
      // Se não há chave armazenada nem na memória, deve resetar verificação
      setIsVerified(false);
      clearMemoryKey();
    } else if (!hasStoredKey && effectiveKey) {
      // Para chaves sem storage (apenas na memória), limpa apenas a chave da memória
      clearMemoryKey();
    }
  }, [clearMemoryKey, setIsVerified]);

  const forceResetVerification = useCallback(() => {
    // Força reset da verificação (usado em navegação)
    setIsVerified(false);
    clearMemoryKey();
  }, [setIsVerified, clearMemoryKey]);

  return {
    validateSecretKey,
    handleSecretKeyError,
    openSecretKeyDialog,
    resetVerificationIfNoStorage,
    forceResetVerification,
    isVerified
  };
};
