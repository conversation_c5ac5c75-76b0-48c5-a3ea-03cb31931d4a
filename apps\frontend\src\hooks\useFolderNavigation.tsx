import { useQueryClient } from "@tanstack/react-query";
import { useNavigate, useLocation } from "react-router";
import { useReportListActions } from "~/store/reportListStore";
import { useBreadcrumbsActions } from "~/store/breadcrumbsStore";
import { useSecretKeyValidation } from "~/hooks/useSecretKeyValidation";

export const useFolderNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryClient = useQueryClient();
  const { resetPagination, clearFolderPath } = useReportListActions();
  const { clearItems, setDirectNavigationMode } = useBreadcrumbsActions();
  const { resetVerificationIfNoStorage } = useSecretKeyValidation();

  const navigateToFolder = (folderId: string) => {
    resetPagination();
    const isDirect = !location.key || location.key === "default";
    setDirectNavigationMode(isDirect);

    navigate(`/pasta/${folderId}`);
  };

  const navigateToRoot = async () => {
    await resetPagination();
    clearFolderPath();
    clearItems();

    // Reset queries first
    queryClient.resetQueries({
      queryKey: ["reports", "list", null],
      exact: true,
    });

    // Check if verification needs to be reset for non-storage scenarios
    // This ensures that if the user has no stored key, they'll be prompted again
    resetVerificationIfNoStorage();

    navigate("/");
  };

  const invalidateCurrentFolder = (folderId?: string | null) => {
    queryClient.invalidateQueries({
      queryKey: ["reports", "list", folderId],
      exact: true,
    });
  };

  return {
    navigateToFolder,
    navigateToRoot,
    invalidateCurrentFolder,
  };
};