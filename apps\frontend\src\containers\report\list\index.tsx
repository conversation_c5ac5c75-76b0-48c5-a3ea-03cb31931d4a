import { useEffect, useRef } from "react";
import { useParams } from "react-router";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import ReportsList from "~/containers/report/ReportsList";
import { useDialogActions } from "~/store/dialogStore";
import { CreateReportDialog } from "~/containers/report/CreateReportDialog";
import { Folder } from "lucide-react";
import { toast } from "sonner";
import MissingKey from "~/components/MissingKey";
import { useReportList } from "~/store/reportListStore";
import { ReportListSkeleton } from "~/components/ReportListSkeleton";
import { useSecretKeyValidation } from "~/hooks/useSecretKeyValidation";
import { useReportListActions } from "~/store/reportListStore";

const ReportListContainer = () => {
  const { folderId } = useParams<{ folderId?: string }>();
  const { openDialog } = useDialogActions();
  const { reportListQuery, invalidateCurrentFolder } = useReportCRUD(folderId || null);
  const { data: reportDataResponse, isFetched: isReportsFetched, isLoading: isReportsLoading, isRefetching: isReportsRefetching, isFetching: isReportsFetching } =
    reportListQuery;
  const reportList = useReportList();
  const { clearFolderPath } = useReportListActions();
  const hasShownToastKey = "reports_list_toast_shown";
  const firstLoadRef = useRef(
    sessionStorage.getItem(hasShownToastKey) !== "true"
  );
  const { openSecretKeyDialog, isVerified } = useSecretKeyValidation();
  const prevFolderIdRef = useRef<string | undefined>(folderId);
  const hasDataLoadedRef = useRef(false);

  useEffect(() => {
    if (!folderId && prevFolderIdRef.current) {
      clearFolderPath();
    };

    if (prevFolderIdRef.current !== folderId) {
      invalidateCurrentFolder(folderId || null);
      prevFolderIdRef.current = folderId;
      // Reset flag quando navega para nova pasta
      hasDataLoadedRef.current = false;
    }
  }, [folderId, invalidateCurrentFolder]);

  useEffect(() => {
    if (!reportDataResponse) return;

    const { isScheduledRefetch } = reportDataResponse;

    // Marca que dados foram carregados com sucesso
    hasDataLoadedRef.current = true;

    if (firstLoadRef.current || isScheduledRefetch) {
      toast.success("Lista atualizada com sucesso!");
      firstLoadRef.current = false;
      sessionStorage.setItem(hasShownToastKey, "true");
    }
  }, [reportDataResponse]);



  function handleOpenDialog() {
    // TODO - trocar pelo modal do DS quando corrigir bug que quebra altura do componente
    openDialog({
      title: "Criar Novo Relatório ou Pasta",
      icon: <Folder />,
      content: <CreateReportDialog.Content />,
      footer: <CreateReportDialog.Footer />,
      className: "max-w-2xl",
    });

    /*  open({
      modal: () => ({
        title: "Criar Novo Relatório ou Pasta",
        icon: <Folder />,
        content: <CreateReportDialog.Content />,
        footer: <CreateReportDialog.Footer />,
      }),
      config: {
        content: {
          className: "max-w-xl",
        },
      },
    }); */
  }
  const renderListContent = () => {
    // Se não está verificado E não há dados carregados, mostra MissingKey
    if (!isVerified && !hasDataLoadedRef.current) {
      return <MissingKey onInsertKey={openSecretKeyDialog} />;
    }

    if (isReportsLoading || isReportsFetching || isReportsRefetching) {
      return <ReportListSkeleton />;
    }

    return (
      <ReportsList
        list={reportList}
        onNewReport={handleOpenDialog}
        isFetched={isReportsFetched}
      />
    );
  };

  return renderListContent();
};

export default ReportListContainer;
