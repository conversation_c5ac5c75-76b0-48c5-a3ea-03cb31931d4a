import { create } from "zustand";

export type StorageDuration = "1m" | "15m" | "30m" | "1h" | "session";

interface StoredKeyData {
  key: string;
  expiresAt: number;
  duration: StorageDuration;
}

interface MemoryKeyData {
  key: string;
}

interface SecretKeyState {
  memoryKey: string | null;
  secretKey: string | null;
  storageDuration: StorageDuration | null;
  selectedDuration: StorageDuration | "none";
  actions: {
    setSecretKey: (key: string, duration?: StorageDuration | "none", salt?: string) => void;
    setSelectedDuration: (duration: StorageDuration | "none") => void;
    clearSecretKey: () => void;
    clearMemoryKey: () => void;
    restoreStoredKey: () => void;
  };
}

const STORAGE_KEY = "enc_key";

const getDurationInMs = (duration: StorageDuration): number => {
  switch (duration) {
    case "1m": return 1 * 60 * 1000;
    case "15m": return 15 * 60 * 1000;
    case "30m": return 30 * 60 * 1000;
    case "1h": return 60 * 60 * 1000;
    case "session": return 0; // sessionStorage
  }
};

const arrayBufferToBase64 = (buffer: ArrayBuffer): string => {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
};

const base64ToArrayBuffer = (base64: string): ArrayBuffer => {
  const binary = atob(base64);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return bytes.buffer;
};

const storeEncryptionKey = async (key: string, salt: string, duration: StorageDuration): Promise<void> => {
  try {
    const { deriveEncryptionKeyArgon2 } = await import("~/helpers/encryption.helper");
    const cryptoKey = await deriveEncryptionKeyArgon2(key, salt);
    const rawKey = await crypto.subtle.exportKey("raw", cryptoKey);
    const b64Key = arrayBufferToBase64(rawKey);

    const data: StoredKeyData = {
      key: b64Key,
      expiresAt: duration === "session" ? 0 : Date.now() + getDurationInMs(duration),
      duration
    };

    const storage = duration === "session" ? sessionStorage : localStorage;
    storage.setItem(STORAGE_KEY, JSON.stringify(data));
  } catch (error) {
    console.error("Erro ao armazenar chave:", error);
  }
};

const getStoredEncryptionKey = (): string | null => {
  try {
    const sessionData = sessionStorage.getItem(STORAGE_KEY);
    const localData = localStorage.getItem(STORAGE_KEY);

    // Prioriza sessionStorage se existir
    const dataStr = sessionData || localData;
    if (!dataStr) return null;

    const data: StoredKeyData = JSON.parse(dataStr);

    // Verifica expiração (0 = session, nunca expira enquanto tab estiver aberta)
    if (data.expiresAt !== 0 && Date.now() > data.expiresAt) {
      // Remove chave expirada
      sessionStorage.removeItem(STORAGE_KEY);
      localStorage.removeItem(STORAGE_KEY);
      return null;
    }

    return data.key;
  } catch (error) {
    console.error("Erro ao recuperar chave armazenada:", error);
    return null;
  }
};

const clearStoredKey = (): void => {
  sessionStorage.removeItem(STORAGE_KEY);
  localStorage.removeItem(STORAGE_KEY);
};

const useSecretKeyStore = create<SecretKeyState>((set, get) => ({
  memoryKey: null,
  secretKey: null,
  storageDuration: null,
  selectedDuration: "none",
  actions: {
    setSecretKey: (key: string, duration?: StorageDuration | "none", salt?: string) => {
      if (duration && duration !== "none" && salt) {
        // Para chaves com armazenamento: armazena no localStorage e limpa da memória
        storeEncryptionKey(key, salt, duration as StorageDuration);
        set((state) => ({
          ...state,
          secretKey: null, // Não armazena na memória quando há storage
          storageDuration: duration as StorageDuration,
        }));
      } else {
        // Para chaves sem armazenamento: mantém apenas na memória temporariamente
        set((state) => ({
          ...state,
          secretKey: key,
          storageDuration: null,
        }));
      }
    },
    setSelectedDuration: (duration: StorageDuration | "none") => {
      set((state) => ({ ...state, selectedDuration: duration }));
    },
    clearSecretKey: () => {
      set({ secretKey: null, storageDuration: null, selectedDuration: "none" });
      clearStoredKey();
    },
    clearMemoryKey: () => {
      // Limpa apenas a chave da memória, mantém storage se existir
      set((state) => ({ ...state, secretKey: null }));
    },
    restoreStoredKey: async () => {
      const storedKey = getStoredEncryptionKey();
      if (storedKey) {
        try {
          // Reconstrói a chave CryptoKey
          const rawKey = base64ToArrayBuffer(storedKey);
          const cryptoKey = await crypto.subtle.importKey(
            "raw",
            rawKey,
            { name: "AES-GCM" },
            false,
            ["encrypt", "decrypt"]
          );

          // Armazena indicador de que temos uma chave válida restaurada
          set({ secretKey: "__STORED_KEY__", storageDuration: "session" });
        } catch (error) {
          console.error("Erro ao restaurar chave:", error);
          clearStoredKey();
        }
      }
    },
  },
}));

export const useSecretKey = () => useSecretKeyStore((state) => state.secretKey);
export const useStorageDuration = () => useSecretKeyStore((state) => state.storageDuration);
export const useSelectedDuration = () => useSecretKeyStore((state) => state.selectedDuration);
export const useSecretKeyActions = () => useSecretKeyStore((state) => state.actions);

// Export das funções para uso em outros locais
export { getStoredEncryptionKey, base64ToArrayBuffer };
