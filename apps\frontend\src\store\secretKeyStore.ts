import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { arrayBufferToBase64, base64ToArrayBuffer } from "~/helpers";

export type StorageDuration = "1m" | "15m" | "30m" | "1h" | "session";

interface StoredKeyData {
  key: string;
  expiresAt: number;
  duration: StorageDuration;
}

interface SecretKeyState {
  // Persistent state (stored)
  storedKeyData: StoredKeyData | null;
  selectedDuration: StorageDuration | "none";

  // Memory-only state (not persisted)
  memoryKey: string | null;
  secretKey: string | null;
  storageDuration: StorageDuration | null;
}

interface SecretKeyActions {
  setSecretKey: (key: string, duration?: StorageDuration | "none", salt?: string) => void;
  setSelectedDuration: (duration: StorageDuration | "none") => void;
  clearSecretKey: () => void;
  clearMemoryKey: () => void;
  restoreStoredKey: () => void;
  hasValidStoredKey: () => boolean;
  getEffectiveSecretKey: () => string | null;
}

interface SecretKeyStore extends SecretKeyState {
  actions: SecretKeyActions;
}

const getDurationInMs = (duration: StorageDuration): number => {
  switch (duration) {
    case "1m": return 1 * 60 * 1000;
    case "15m": return 15 * 60 * 1000;
    case "30m": return 30 * 60 * 1000;
    case "1h": return 60 * 60 * 1000;
    case "session": return 0; // sessionStorage
  }
};

// Legacy function for backward compatibility - now uses store data
const getStoredEncryptionKey = (): string | null => {
  const state = useSecretKeyStore.getState();
  const storedData = state.storedKeyData;

  if (!storedData) return null;

  // Verifica expiração
  if (storedData.expiresAt !== 0 && Date.now() > storedData.expiresAt) {
    // Remove chave expirada via actions
    state.actions.clearSecretKey();
    return null;
  }

  return storedData.key;
};

// Create the store with persist middleware for selected parts
const useSecretKeyStore = create<SecretKeyStore>()(
  persist(
    (set, get) => ({
      // Persistent state
      storedKeyData: null,
      selectedDuration: "none",

      // Memory-only state (will be reset on page reload)
      memoryKey: null,
      secretKey: null,
      storageDuration: null,

      actions: {
        setSecretKey: async (key: string, duration?: StorageDuration | "none", salt?: string) => {
          if (duration && duration !== "none" && salt) {
            // Para chaves com armazenamento: cria dados persistentes
            try {
              const { deriveEncryptionKeyArgon2 } = await import("~/helpers/encryption.helper");
              const cryptoKey = await deriveEncryptionKeyArgon2(key, salt);
              const rawKey = await crypto.subtle.exportKey("raw", cryptoKey);
              const b64Key = arrayBufferToBase64(rawKey);

              const storedData: StoredKeyData = {
                key: b64Key,
                expiresAt: duration === "session" ? 0 : Date.now() + getDurationInMs(duration as StorageDuration),
                duration: duration as StorageDuration
              };

              set((state) => ({
                ...state,
                storedKeyData: storedData,
                secretKey: null, // Não armazena na memória quando há storage
                storageDuration: duration as StorageDuration,
              }));
            } catch (error) {
              console.error("Erro ao armazenar chave:", error);
            }
          } else {
            // Para chaves sem armazenamento: mantém apenas na memória temporariamente
            set((state) => ({
              ...state,
              secretKey: key,
              storageDuration: null,
              storedKeyData: null, // Limpa dados armazenados se existirem
            }));
          }
        },

        setSelectedDuration: (duration: StorageDuration | "none") => {
          set((state) => ({ ...state, selectedDuration: duration }));
        },

        clearSecretKey: () => {
          set({
            secretKey: null,
            storageDuration: null,
            selectedDuration: "none",
            storedKeyData: null,
            memoryKey: null
          });
        },

        clearMemoryKey: () => {
          // Limpa apenas a chave da memória, mantém storage se existir
          set((state) => ({ ...state, secretKey: null, memoryKey: null }));
        },

        restoreStoredKey: async () => {
          const state = get();
          const storedData = state.storedKeyData;

          if (storedData) {
            // Verifica expiração
            if (storedData.expiresAt !== 0 && Date.now() > storedData.expiresAt) {
              // Remove chave expirada
              set((state) => ({ ...state, storedKeyData: null }));
              return;
            }

            try {
              // Reconstrói a chave CryptoKey para validação
              const rawKey = base64ToArrayBuffer(storedData.key);
              await crypto.subtle.importKey(
                "raw",
                rawKey,
                { name: "AES-GCM" },
                false,
                ["encrypt", "decrypt"]
              );

              // Armazena indicador de que temos uma chave válida restaurada
              set((state) => ({
                ...state,
                secretKey: "__STORED_KEY__",
                storageDuration: storedData.duration
              }));
            } catch (error) {
              console.error("Erro ao restaurar chave:", error);
              set((state) => ({ ...state, storedKeyData: null }));
            }
          }
        },

        hasValidStoredKey: () => {
          const state = get();
          const storedData = state.storedKeyData;

          if (!storedData) return false;

          // Verifica expiração
          if (storedData.expiresAt !== 0 && Date.now() > storedData.expiresAt) {
            // Remove chave expirada
            set((state) => ({ ...state, storedKeyData: null }));
            return false;
          }

          return true;
        },

        getEffectiveSecretKey: () => {
          const state = get();
          return state.secretKey || (state.storedKeyData ? "__STORED_KEY__" : null);
        },
      },
    }),
    {
      name: "enc_key",
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        storedKeyData: state.storedKeyData,
        selectedDuration: state.selectedDuration,
      }),
    }
  )
);

// Selectors
export const useSecretKey = () => useSecretKeyStore((state) => state.secretKey);
export const useStorageDuration = () => useSecretKeyStore((state) => state.storageDuration);
export const useSelectedDuration = () => useSecretKeyStore((state) => state.selectedDuration);
export const useStoredKeyData = () => useSecretKeyStore((state) => state.storedKeyData);
export const useMemoryKey = () => useSecretKeyStore((state) => state.memoryKey);

// Actions
export const useSecretKeyActions = () => useSecretKeyStore((state) => state.actions);

// Helper functions
export const hasValidStoredKey = () => useSecretKeyStore.getState().actions.hasValidStoredKey();
export const getEffectiveSecretKey = () => useSecretKeyStore.getState().actions.getEffectiveSecretKey();

// Legacy compatibility functions
export { getStoredEncryptionKey, base64ToArrayBuffer };
