import { useCallback } from "react";
import { useEncryption, CryptoResult, SecretKeyExpiredError } from "./useEncryption";
import { useSecretKeyValidation } from "./useSecretKeyValidation";

import { EncryptedPayload } from "~/types/global";

/**
 * Hook que wrappea useEncryption com validação automática de chave secreta
 * Abre o diálogo de chave secreta automaticamente quando necessário
 */
export const useSecureEncryption = () => {
  const { encryptData, decryptData, encryptNgrams, salt } = useEncryption();
  const { handleSecretKeyError, resetVerificationIfNoStorage, forceResetVerification, isVerified } = useSecretKeyValidation();


  // Helper para limpar chave da memória e resetar verificação se não há storage
  const clearKeyIfNoStorage = useCallback(() => {
    resetVerificationIfNoStorage();
  }, [resetVerificationIfNoStorage]);

  const secureEncryptData = useCallback(
    async (data: any): Promise<CryptoResult<EncryptedPayload>> => {
      try {
        const result = await encryptData(data);
        // Limpa chave da memória se não há storage
        clearKeyIfNoStorage();
        return result;
      } catch (error) {
        // Se é erro de chave expirada/faltando, reseta verificação
        if (error instanceof SecretKeyExpiredError) {
          forceResetVerification();
          return {
            success: false,
            error: "Chave secreta necessária - diálogo de autenticação foi aberto",
          };
        }

        const handled = handleSecretKeyError(error as Error);
        if (handled) {
          return {
            success: false,
            error: "Chave secreta expirada - diálogo de autenticação foi aberto",
          };
        }

        console.error("Encryption error:", error);
        return {
          success: false,
          error: error instanceof Error ? error.message : "Unknown encryption error",
        };
      }
    },
    [encryptData, handleSecretKeyError, clearKeyIfNoStorage, forceResetVerification]
  );

  const secureDecryptData = useCallback(
    async (encryptedData: EncryptedPayload): Promise<CryptoResult<any>> => {
      try {
        const result = await decryptData(encryptedData);
        // Limpa chave da memória se não há storage
        clearKeyIfNoStorage();
        return result;
      } catch (error) {
        // Se é erro de chave expirada/faltando, reseta verificação
        if (error instanceof SecretKeyExpiredError) {
          forceResetVerification();
          return {
            success: false,
            error: "Chave secreta necessária - diálogo de autenticação foi aberto",
          };
        }

        const handled = handleSecretKeyError(error as Error);
        if (handled) {
          return {
            success: false,
            error: "Chave secreta expirada - diálogo de autenticação foi aberto",
          };
        }

        console.error("Decryption error:", error);
        return {
          success: false,
          error: error instanceof Error ? error.message : "Unknown decryption error",
        };
      }
    },
    [decryptData, handleSecretKeyError, clearKeyIfNoStorage, forceResetVerification]
  );

  const secureEncryptNgrams = useCallback(
    async (reportObj: Record<string, any>): Promise<Record<string, string[]> | null> => {
      try {
        const result = await encryptNgrams(reportObj);
        // Limpa chave da memória se não há storage
        clearKeyIfNoStorage();
        return result;
      } catch (error) {
        // Se é erro de chave expirada/faltando, reseta verificação
        if (error instanceof SecretKeyExpiredError) {
          forceResetVerification();
          return null;
        }

        const handled = handleSecretKeyError(error as Error);
        if (handled) {
          return null;
        }

        console.error("N-grams encryption error:", error);
        throw error;
      }
    },
    [encryptNgrams, handleSecretKeyError, clearKeyIfNoStorage, forceResetVerification]
  );

  return {
    encryptData: secureEncryptData,
    decryptData: secureDecryptData,
    encryptNgrams: secureEncryptNgrams,
    salt,
    isVerified,
  };
};
