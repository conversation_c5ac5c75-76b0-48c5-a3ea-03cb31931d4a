import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { useParams } from "react-router";
import { toast } from "sonner";
import { useCallback } from "react";
import { useCreateFolderActions } from "~/store/createFolderStore";
import { useDialogActions } from "~/store/dialogStore";
import { createErrorHandler } from "~/helpers/errorHandling.helper";
import { useSecureEncryption } from "~/hooks/useSecureEncryption";
import { EncryptedPayload, FolderPayload, MoveFilesPayload, MergeFoldersPayload, HierarchicalFolderItem } from "~/types/global";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import {
  deleteFolder,
  renameFolder,
  createFolder,
  moveFilesBetweenFolders,
  moveFolderToFolder,
  mergeFolders,
  getHierarchicalFolders,
} from "~/services/gateways/folder.gateway";
import { REPORT_CONSTANTS } from "~/helpers/constants";
import { useFolderListFilters } from "~/store/folderListStore";
import { decryptListItem } from "~/helpers";

export interface CreateFolderRequest {
  folderName: string;
  selectedReports?: string[];
  parentFolderId?: string | null;
}

export interface CreateFolderResponse {
  folder_id: string;
  folder_name: string;
  created_at: string;
  reports_count: number;
}

export interface FolderListQueryParams {
  folder_id?: string;
  hmac_folder_name?: string;
  excludeFolderId?: string;
}


interface MutationSuccessHandlers {
  onFolderCreate: (data: CreateFolderResponse) => void;
  onFolderDelete: () => void;
  onFolderRename: () => void;
  onFilesMove: () => void;
  onReportMove: () => void;
  onFolderMove: () => void;
  onFolderMerge: () => void;
}

const createQueryPayload = (
  queryParams: FolderListQueryParams,
  filters: ReturnType<typeof useFolderListFilters>
) => {
  const payload: {
    folder_id?: string;
    hmac_folder_name?: string;
    column_order: string;
    order: string;
    limit: number;
    page: number;
  } = {
    column_order: filters.column_order,
    order: filters.order,
    limit: filters.limit,
    page: filters.page,
  };

  if (queryParams.excludeFolderId) {
    payload.folder_id = queryParams.excludeFolderId;
  }

  if (queryParams.folder_id) {
    payload.folder_id = queryParams.folder_id;
  }

  if (queryParams.hmac_folder_name) {
    payload.hmac_folder_name = queryParams.hmac_folder_name;
  }

  return payload;
};

const processDecryptedFolders = async (
  data: any[],
  decryptData: (data: any) => Promise<any>
): Promise<HierarchicalFolderItem[]> => {
  const decryptedItems = await Promise.all(
    data.map(async (item, index) => {
      try {
        return await decryptListItem(item, decryptData);
      } catch (error) {
        console.error(`Error decrypting item #${index + 1}:`, error);
        throw error;
      }
    })
  );
  return decryptedItems as HierarchicalFolderItem[];
};

export const useFolderCRUD = () => {
  const { folderId } = useParams<{ folderId?: string }>();
  const { invalidateCurrentFolderNoFilters } = useReportCRUD(folderId || null);
  const queryClient = useQueryClient();
  const { closeDialog } = useDialogActions();
  const { encryptData, encryptNgrams, decryptData } = useSecureEncryption();
  const filters = useFolderListFilters();
  const { clearFolderData } = useCreateFolderActions();

  const successHandlers: MutationSuccessHandlers = {
    onFolderCreate: useCallback((data: CreateFolderResponse) => {
      invalidateCurrentFolderNoFilters(folderId || null);
      clearFolderData();
      closeDialog();
      toast.success(`Pasta criada com sucesso!`, {
        description: `Pasta criada com ${data?.reports_count || 0} relatórios`,
      });
    }, [folderId, invalidateCurrentFolderNoFilters, clearFolderData, closeDialog]),

    onFolderDelete: useCallback(() => {
      invalidateCurrentFolderNoFilters(folderId || null);
      closeDialog();
      toast.success(`Pasta deletada com sucesso!`);
    }, [folderId, invalidateCurrentFolderNoFilters, closeDialog]),

    onFolderRename: useCallback(() => {
      invalidateCurrentFolderNoFilters(folderId || null);
      closeDialog();
      toast.success(`Pasta renomeada com sucesso!`);
    }, [folderId, invalidateCurrentFolderNoFilters, closeDialog]),

    onFilesMove: useCallback(() => {
      invalidateCurrentFolderNoFilters(folderId || null);
      closeDialog();
      toast.success(`Arquivos movidos com sucesso!`);
    }, [folderId, invalidateCurrentFolderNoFilters, closeDialog]),

    onReportMove: useCallback(() => {
      closeDialog();
      toast.success(`Relatório movido com sucesso!`);
    }, [closeDialog]),

    onFolderMove: useCallback(() => {
      closeDialog();
      toast.success(`Pasta movida com sucesso!`);
    }, [closeDialog]),

    onFolderMerge: useCallback(() => {
      closeDialog();
      toast.success(`Pastas mescladas com sucesso!`);
    }, [closeDialog]),
  };

  const createFolderMutation = useMutation<CreateFolderResponse, Error, FolderPayload>({
    mutationFn: async (payload: FolderPayload): Promise<CreateFolderResponse> => {
      const folderName = payload.folderName;
      console.log("[useFolderCRUD] createFolderMutation folderName", folderName);

      const encryptedFolderName = await encryptData(folderName);
      if (!encryptedFolderName.success || !encryptedFolderName.data) {
        throw new Error("Erro ao criptografar nome da pasta - autenticação necessária");
      }

      const hmacResult = await encryptNgrams({ folder_name: folderName });
      if (!hmacResult) {
        throw new Error("Erro ao criptografar n-grams - autenticação necessária");
      }

      const apiPayload: FolderPayload = {
        [REPORT_CONSTANTS.new_folder.folder_name]: encryptedFolderName.data as EncryptedPayload,
        [REPORT_CONSTANTS.new_folder.parent_folder_id]: folderId || null,
        [REPORT_CONSTANTS.new_folder.user_reports_id_list]: payload.selectedReports || [],
        [REPORT_CONSTANTS.new_folder.hmac_folder_name]: hmacResult.folder_name || [],
      };

      console.log("[useFolderCRUD] createFolderMutation payload", apiPayload);
      return createFolder(apiPayload) as Promise<CreateFolderResponse>;
    },
    onSuccess: successHandlers.onFolderCreate,
    onError: createErrorHandler(
      "Ocorreu um erro ao tentar criar a pasta",
      "Erro ao criar pasta"
    ),
  });

  const deleteFolderMutation = useMutation({
    mutationFn: (folderId: string) => deleteFolder(folderId),
    onSuccess: successHandlers.onFolderDelete,
    onError: createErrorHandler(
      "Ocorreu um erro ao tentar deletar a pasta",
      "Erro ao deletar pasta"
    ),
  });

  const renameFolderMutation = useMutation({
    mutationFn: async (payload: FolderPayload) => {
      const hmacResult = await encryptNgrams({ folder_name: payload.folder_name });
      if (!hmacResult) {
        throw new Error("Erro ao criptografar n-grams - autenticação necessária");
      }

      const encryptedFolderName = await encryptData(payload.folder_name);
      if (!encryptedFolderName.success || !encryptedFolderName.data) {
        throw new Error("Erro ao criptografar nome da pasta - autenticação necessária");
      }

      return renameFolder({
        [REPORT_CONSTANTS.new_folder.folder_id]: payload.folder_id,
        [REPORT_CONSTANTS.new_folder.folder_name]: encryptedFolderName.data as EncryptedPayload,
        [REPORT_CONSTANTS.new_folder.hmac_folder_name]: hmacResult.folder_name || [],
      });
    },
    onSuccess: successHandlers.onFolderRename,
    onError: createErrorHandler(
      "Ocorreu um erro ao tentar renomear a pasta",
      "Erro ao renomear pasta"
    ),
  });

  const moveFilesBetweenFoldersMutation = useMutation({
    mutationFn: (payload: MoveFilesPayload) => moveFilesBetweenFolders(payload),
    onSuccess: successHandlers.onFilesMove,
    onError: createErrorHandler(
      "Ocorreu um erro ao tentar mover os arquivos",
      "Erro ao mover arquivos"
    ),
  });

  const moveReportToFolderMutation = useMutation({
    mutationFn: async (payload: {
      reportId: string;
      sourceFolderId?: string | null;
      destinationFolderId?: string | null;
    }) => {
      const movePayload: MoveFilesPayload = {
        [REPORT_CONSTANTS.move_files.src_folder_id]: payload.sourceFolderId || null,
        [REPORT_CONSTANTS.move_files.dest_folder_id]: payload.destinationFolderId || null,
        [REPORT_CONSTANTS.move_files.user_reports_id_list]: [payload.reportId],
        [REPORT_CONSTANTS.move_files.folder_id_list]: null,
      };

      console.log("[useFolderCRUD] moveReportToFolderMutation payload", movePayload);
      return moveFilesBetweenFolders(movePayload);
    },
    onSuccess: (data, variables) => {
      invalidateCurrentFolderNoFilters(variables.sourceFolderId || null);
      if (variables.destinationFolderId !== variables.sourceFolderId) {
        invalidateCurrentFolderNoFilters(variables.destinationFolderId || null);
      }
      successHandlers.onReportMove();
    },
    onError: createErrorHandler(
      "Ocorreu um erro ao tentar mover o relatório",
      "Erro ao mover relatório"
    ),
  });

  const moveFolderToFolderMutation = useMutation({
    mutationFn: async (payload: {
      folderId: string;
      sourceFolderId?: string | null;
      destinationFolderId?: string | null;
    }) => {
      const movePayload: MoveFilesPayload = {
        [REPORT_CONSTANTS.move_files.src_folder_id]: payload.sourceFolderId || null,
        [REPORT_CONSTANTS.move_files.dest_folder_id]: payload.destinationFolderId || null,
        [REPORT_CONSTANTS.move_files.user_reports_id_list]: null,
        [REPORT_CONSTANTS.move_files.folder_id_list]: [payload.folderId],
      };

      console.log("[useFolderCRUD] moveFolderToFolderMutation payload", movePayload);
      return moveFolderToFolder(movePayload);
    },
    onSuccess: (data, variables) => {
      invalidateCurrentFolderNoFilters(variables.sourceFolderId || null);
      if (variables.destinationFolderId !== variables.sourceFolderId) {
        invalidateCurrentFolderNoFilters(variables.destinationFolderId || null);
      }
      successHandlers.onFolderMove();
    },
    onError: createErrorHandler(
      "Ocorreu um erro ao tentar mover a pasta",
      "Erro ao mover pasta"
    ),
  });

  const mergeFoldersMutation = useMutation({
    mutationFn: async (payload: {
      folderId: string | null;
      folderIdToMerge: string;
    }) => {
      const mergePayload: MergeFoldersPayload = {
        [REPORT_CONSTANTS.merge_folders.folder_id]: payload.folderId,
        [REPORT_CONSTANTS.merge_folders.folder_id_to_merge]: payload.folderIdToMerge,
      };

      console.log("[useFolderCRUD] mergeFoldersMutation payload", mergePayload);
      return mergeFolders(mergePayload);
    },
    onSuccess: () => {
      invalidateCurrentFolderNoFilters(folderId || null);
      successHandlers.onFolderMerge();
    },
    onError: createErrorHandler(
      "Ocorreu um erro ao tentar mesclar as pastas",
      "Erro ao mesclar pastas"
    ),
  });

  function useHierarchicalFoldersQuery(excludeFolderId?: string) {
    return useQuery({
      queryKey: ["hierarchical-folders", excludeFolderId],
      queryFn: async () => {
        const folders = await getHierarchicalFolders({
          column_order: "created_at",
          order: "desc",
        });
        return folders;
      },
      staleTime: Infinity,
    });
  }

  return {
    createFolderMutation,
    deleteFolderMutation,
    renameFolderMutation,
    moveFilesBetweenFoldersMutation,
    moveReportToFolderMutation,
    moveFolderToFolderMutation,
    mergeFoldersMutation,
    useHierarchicalFoldersQuery,
  };
};
